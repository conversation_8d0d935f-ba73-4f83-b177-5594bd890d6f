import { describe, expect, test } from "@jest/globals";
import { getPeriodNamings } from "./GetPeriodNames";

describe("getPeriodNamings", () => {
	describe("Calendar month periods (Type A)", () => {
		test("should handle single calendar month periods in English", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-31" },
				{ from: "2024-02-01", to: "2024-02-29" }, // Leap year
				{ from: "2024-03-01", to: "2024-03-31" },
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					graphItemLabel: "Jan",
					graphDescLabel: undefined,
					textName: "January",
				},
				{
					graphItemLabel: "Feb",
					graphDescLabel: undefined,
					textName: "February",
				},
				{
					graphItemLabel: "Mar",
					graphDescLabel: undefined,
					textName: "March",
				},
			]);
		});

		test("should handle calendar month periods in German", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-31" },
				{ from: "2024-02-01", to: "2024-02-29" },
			];

			const result = getPeriodNamings(periods, "de", "standard");

			expect(result).toEqual([
				{
					graphItemLabel: "Jan.",
					graphDescLabel: undefined,
					textName: "Januar",
				},
				{
					graphItemLabel: "Feb.",
					graphDescLabel: undefined,
					textName: "Februar",
				},
			]);
		});

		test("should handle calendar month periods in French", () => {
			const periods = [
				{ from: "2024-06-01", to: "2024-06-30" },
				{ from: "2024-07-01", to: "2024-07-31" },
			];

			const result = getPeriodNamings(periods, "fr", "standard");

			expect(result).toEqual([
				{
					graphItemLabel: "juin",
					graphDescLabel: undefined,
					textName: "juin",
				},
				{
					graphItemLabel: "juil.",
					graphDescLabel: undefined,
					textName: "juillet",
				},
			]);
		});
	});

	describe("Range of calendar months periods (Type B)", () => {
		test("should handle range of calendar months in English", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-03-31" }, // Jan-Mar
				{ from: "2024-04-01", to: "2024-06-30" }, // Apr-Jun
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					graphItemLabel: "Jan–Mar",
					graphDescLabel: undefined,
					textName: "January–March",
				},
				{
					graphItemLabel: "Apr–Jun",
					graphDescLabel: undefined,
					textName: "April–June",
				},
			]);
		});

		test("should handle range of calendar months in German", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-02-29" }, // Jan-Feb
			];

			const result = getPeriodNamings(periods, "de", "standard");

			expect(result).toEqual([
				{
					graphItemLabel: "Jan.–Feb.",
					graphDescLabel: undefined,
					textName: "Januar–Februar",
				},
			]);
		});

		test("should handle cross-year range of calendar months", () => {
			const periods = [
				{ from: "2023-11-01", to: "2024-01-31" }, // Nov-Jan
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					graphItemLabel: "Nov–Jan",
					graphDescLabel: undefined,
					textName: "November–January",
				},
			]);
		});
	});

	describe("Same length periods (Type C)", () => {
		test("should handle weekly periods", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-07" }, // 7 days
				{ from: "2024-01-08", to: "2024-01-14" }, // 7 days
				{ from: "2024-01-15", to: "2024-01-21" }, // 7 days
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					graphItemLabel: "Jan 7",
					graphDescLabel: "1-week periods",
					textName: "1-week period ending January 7",
				},
				{
					graphItemLabel: "Jan 14",
					graphDescLabel: "1-week periods",
					textName: "1-week period ending January 14",
				},
				{
					graphItemLabel: "Jan 21",
					graphDescLabel: "1-week periods",
					textName: "Latest 1-week period",
				},
			]);
		});

		test("should handle 4-week periods", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-28" }, // 28 days = 4 weeks
				{ from: "2024-01-29", to: "2024-02-25" }, // 28 days = 4 weeks
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					graphItemLabel: "Jan 28",
					graphDescLabel: "4-week periods",
					textName: "4-week period ending January 28",
				},
				{
					graphItemLabel: "Feb 25",
					graphDescLabel: "4-week periods",
					textName: "Latest 4-week period",
				},
			]);
		});

		test("should handle non-weekly periods (days)", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-20" }, // 20 days
				{ from: "2024-01-21", to: "2024-02-09" }, // 20 days
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					graphItemLabel: "Jan 20",
					graphDescLabel: "20-day periods",
					textName: "20-day period ending January 20",
				},
				{
					graphItemLabel: "Feb 9",
					graphDescLabel: "20-day periods",
					textName: "Latest 20-day period",
				},
			]);
		});

		test("should handle same length periods in German", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-07" }, // 7 days
				{ from: "2024-01-08", to: "2024-01-14" }, // 7 days
			];

			const result = getPeriodNamings(periods, "de", "standard");

			expect(result).toEqual([
				{
					graphItemLabel: "7. Jan.",
					graphDescLabel: "1-week periods",
					textName: "1-week period ending 7. Januar",
				},
				{
					graphItemLabel: "14. Jan.",
					graphDescLabel: "1-week periods",
					textName: "Latest 1-week period",
				},
			]);
		});
	});

	describe("Edge cases", () => {
		test("should handle empty periods array", () => {
			const result = getPeriodNamings([], "en", "standard");
			expect(result).toEqual([]);
		});

		test("should handle single period", () => {
			const periods = [{ from: "2024-01-01", to: "2024-01-31" }];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					graphItemLabel: "Jan",
					graphDescLabel: undefined,
					textName: "January",
				},
			]);
		});

		test("should handle invalid language code gracefully", () => {
			const periods = [{ from: "2024-01-01", to: "2024-01-31" }];

			// Should not throw an error, but use fallback behavior
			const result = getPeriodNamings(periods, "invalid-lang", "standard");
			expect(result).toHaveLength(1);
			expect(result[0].label).toBeDefined();
		});

		test("should handle leap year February correctly", () => {
			const periods = [
				{ from: "2024-02-01", to: "2024-02-29" }, // Leap year
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					graphItemLabel: "Feb",
					graphDescLabel: undefined,
					textName: "February",
				},
			]);
		});

		test("should handle non-leap year February correctly", () => {
			const periods = [
				{ from: "2023-02-01", to: "2023-02-28" }, // Non-leap year
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					graphItemLabel: "Feb",
					graphDescLabel: undefined,
					textName: "February",
				},
			]);
		});
	});

	describe("Period type detection", () => {
		test("should not treat partial month as calendar month", () => {
			const periods = [
				{ from: "2024-01-02", to: "2024-01-31" }, // Not starting from 1st
			];

			const result = getPeriodNamings(periods, "en", "standard");

			// Should be treated as same-length period, not calendar month
			expect(result[0].labelDesc).toBeDefined(); // Should have period description
			expect(result[0].textName).toContain("Latest");
		});

		test("should not treat partial month end as calendar month", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-30" }, // Not ending on last day
			];

			const result = getPeriodNamings(periods, "en", "standard");

			// Should be treated as same-length period, not calendar month
			expect(result[0].labelDesc).toBeDefined(); // Should have period description
			expect(result[0].textName).toContain("Latest");
		});

		test("should show years when 11+ months difference", () => {
			const periods = [
				{ from: "2023-01-01", to: "2023-01-07" }, // 12 months before latest (should show year)
				{ from: "2023-03-01", to: "2023-03-07" }, // 10 months before latest (should not show year)
				{ from: "2024-01-01", to: "2024-01-07" }, // Latest year
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					graphItemLabel: "Jan 7, 2023", // Shows year (12 months difference >= 11)
					graphDescLabel: "1-week periods",
					textName: "1-week period ending January 7, 2023",
				},
				{
					graphItemLabel: "Mar 7", // No year (10 months difference < 11)
					graphDescLabel: "1-week periods",
					textName: "1-week period ending March 7",
				},
				{
					graphItemLabel: "Jan 7", // No year (latest)
					graphDescLabel: "1-week periods",
					textName: "Latest 1-week period",
				},
			]);
		});
	});

	describe("Thousand-days calendar format (dateFormat: 't')", () => {
		test("should use moment format 't' for thousand-days calendar", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-07" }, // 7 days
				{ from: "2024-01-08", to: "2024-01-14" }, // 7 days
			];

			const result = getPeriodNamings(periods, "en", "t");

			// The 't' format produces thousand-days calendar dates like "19-095"
			expect(result).toEqual([
				{
					graphItemLabel: "19-095",
					graphDescLabel: "1-week periods",
					textName: "1-week period ending 19-095",
				},
				{
					graphItemLabel: "19-102",
					graphDescLabel: "1-week periods",
					textName: "Latest 1-week period",
				},
			]);
		});

		test("should use moment format 't' for calendar months with thousand-days format", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-31" },
				{ from: "2024-02-01", to: "2024-02-29" },
			];

			const result = getPeriodNamings(periods, "en", "t");

			// For calendar months, the thousand-days format doesn't apply to month names
			// Only to date formatting in Type C periods
			expect(result).toEqual([
				{
					graphItemLabel: "Jan",
					graphDescLabel: undefined,
					textName: "January",
				},
				{
					graphItemLabel: "Feb",
					graphDescLabel: undefined,
					textName: "February",
				},
			]);
		});
	});
});
